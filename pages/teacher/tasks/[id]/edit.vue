<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载任务信息时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 编辑表单 -->
    <div v-else-if="task">
      <!-- 页面头部 -->
      <div class="flex items-center space-x-4 mb-8">
        <UButton 
          variant="ghost" 
          icon="i-heroicons-arrow-left"
          @click="navigateTo(`/teacher/tasks/${taskId}`)"
        >
          返回任务详情
        </UButton>
        <div>
          <h1 class="text-3xl font-bold text-gray-900">编辑任务</h1>
          <p class="text-gray-600 mt-2">修改任务信息和设置</p>
        </div>
      </div>

      <!-- 编辑表单 -->
      <UCard class="max-w-4xl">
        <template #header>
          <h2 class="text-xl font-semibold text-gray-900">任务信息</h2>
        </template>

        <form @submit.prevent="updateTask" class="space-y-6">
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- 基本信息 -->
            <div class="space-y-4">
              <UFormGroup label="任务标题" required>
                <UInput 
                  v-model="editForm.title" 
                  placeholder="请输入任务标题"
                  :disabled="updating"
                />
              </UFormGroup>

              <UFormGroup label="任务描述">
                <UTextarea 
                  v-model="editForm.description" 
                  placeholder="请输入任务描述（可选）"
                  :disabled="updating"
                  :rows="4"
                />
              </UFormGroup>

              <UFormGroup label="截止时间">
                <UInput 
                  v-model="editForm.due_date" 
                  type="datetime-local"
                  :disabled="updating"
                />
              </UFormGroup>
            </div>

            <!-- 项目信息 -->
            <div class="space-y-4">
              <UFormGroup label="所属项目">
                <UInput 
                  :value="task.project_name" 
                  disabled
                  placeholder="项目名称"
                />
                <p class="text-xs text-gray-500 mt-1">任务创建后不能更改所属项目</p>
              </UFormGroup>

              <UFormGroup label="任务状态">
                <USelect 
                  v-model="editForm.status"
                  :options="statusOptions"
                  :disabled="updating"
                />
              </UFormGroup>

              <div class="bg-gray-50 p-4 rounded-lg">
                <h3 class="font-medium text-gray-900 mb-2">任务统计</h3>
                <div class="space-y-2 text-sm text-gray-600">
                  <div class="flex justify-between">
                    <span>分配学生:</span>
                    <span>{{ task.student_count || 0 }} 人</span>
                  </div>
                  <div class="flex justify-between">
                    <span>已提交:</span>
                    <span>{{ task.submitted_count || 0 }} 人</span>
                  </div>
                  <div class="flex justify-between">
                    <span>待审核:</span>
                    <span>{{ task.pending_count || 0 }} 人</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- 操作按钮 -->
          <div class="flex justify-end space-x-3 pt-6 border-t">
            <UButton 
              variant="outline" 
              @click="navigateTo(`/teacher/tasks/${taskId}`)"
              :disabled="updating"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="updating"
            >
              保存更改
            </UButton>
          </div>
        </form>
      </UCard>
    </div>

    <!-- 任务不存在 -->
    <div v-else class="text-center py-12">
      <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">任务不存在</h3>
      <p class="text-gray-500 mb-4">请检查任务ID是否正确</p>
      <UButton @click="navigateTo('/teacher/tasks')" variant="outline">
        返回任务列表
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const updating = ref(false)

// 获取路由参数
const route = useRoute()
const taskId = parseInt(route.params.id as string)

// 添加调试信息
console.log('任务编辑页面加载，任务ID:', taskId)

// 获取任务详情
const { data: taskResponse, pending, error, refresh } = await useAuthFetch(`/api/teacher/tasks/${taskId}`, {
  server: false,
  default: () => ({ success: false, data: null }),
  key: `task-edit-${taskId}`
})

// 提取数据
const task = computed(() => (taskResponse.value as any)?.data || null)

// 状态选项
const statusOptions = [
  { label: '进行中', value: 'active' },
  { label: '已结束', value: 'completed' }
]

// 编辑表单
const editForm = ref({
  title: '',
  description: '',
  due_date: '',
  status: 'active'
})

// 监听任务数据变化，初始化表单
watch(task, (newTask) => {
  if (newTask) {
    editForm.value = {
      title: newTask.title || '',
      description: newTask.description || '',
      due_date: newTask.due_date ? new Date(newTask.due_date).toISOString().slice(0, 16) : '',
      status: newTask.status || 'active'
    }
  }
}, { immediate: true })

// 更新任务
const updateTask = async () => {
  updating.value = true
  try {
    const accessToken = useCookie('accessToken')
    
    // 准备更新数据
    const updateData = {
      title: editForm.value.title,
      description: editForm.value.description,
      due_date: editForm.value.due_date || null,
      status: editForm.value.status
    }
    
    await $fetch(`/api/teacher/tasks/${taskId}`, {
      method: 'PUT',
      body: updateData,
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 跳转回任务详情页面
    navigateTo(`/teacher/tasks/${taskId}`)
  } catch (error) {
    console.error('更新任务失败:', error)
    // TODO: 显示错误消息
  } finally {
    updating.value = false
  }
}

// Set page title
useHead({
  title: computed(() => task.value ? `编辑 ${task.value.title}` : '编辑任务'),
  meta: [
    { name: 'description', content: '编辑任务信息和设置' }
  ]
})
</script>
