<template>
  <div class="container mx-auto px-4 py-8">
    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载任务详情时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 任务详情内容 -->
    <div v-else-if="task">
      <!-- 页面头部 -->
      <div class="flex justify-between items-start mb-8">
        <div class="flex items-center space-x-4">
          <UButton 
            variant="ghost" 
            icon="i-heroicons-arrow-left"
            @click="navigateTo('/teacher/tasks')"
          >
            返回任务列表
          </UButton>
          <div>
            <h1 class="text-3xl font-bold text-gray-900">{{ task.title }}</h1>
            <p class="text-gray-600 mt-2">任务详情管理</p>
          </div>
        </div>
        
        <div class="flex space-x-3">
          <UButton 
            variant="outline" 
            icon="i-heroicons-pencil"
            @click="editTask"
          >
            编辑任务
          </UButton>
          <UButton 
            color="red" 
            variant="outline"
            icon="i-heroicons-trash"
            @click="deleteTask"
          >
            删除任务
          </UButton>
        </div>
      </div>

      <!-- 任务基本信息 -->
      <div class="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
        <UCard class="lg:col-span-2">
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">任务信息</h2>
          </template>
          
          <div class="space-y-4">
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">任务标题</label>
              <p class="text-gray-900">{{ task.title }}</p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">任务描述</label>
              <p class="text-gray-900">
                {{ task.description || '暂无描述' }}
              </p>
            </div>
            
            <div>
              <label class="block text-sm font-medium text-gray-700 mb-1">所属项目</label>
              <p class="text-gray-900">{{ task.project_name }}</p>
            </div>
            
            <div class="grid grid-cols-2 gap-4">
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">创建时间</label>
                <p class="text-gray-900">{{ formatDate(task.created_at) }}</p>
              </div>
              <div>
                <label class="block text-sm font-medium text-gray-700 mb-1">截止时间</label>
                <p class="text-gray-900">
                  {{ task.due_date ? formatDate(task.due_date) : '无截止时间' }}
                </p>
              </div>
            </div>
          </div>
        </UCard>

        <!-- 统计信息 -->
        <UCard>
          <template #header>
            <h2 class="text-xl font-semibold text-gray-900">任务统计</h2>
          </template>
          
          <div class="space-y-4">
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-users" class="text-blue-600 mr-2" />
                <span class="text-sm text-gray-600">分配学生</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ task.student_count || 0 }}</span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-document-check" class="text-green-600 mr-2" />
                <span class="text-sm text-gray-600">已提交</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ task.submitted_count || 0 }}</span>
            </div>
            
            <div class="flex items-center justify-between">
              <div class="flex items-center">
                <UIcon name="i-heroicons-clock" class="text-orange-600 mr-2" />
                <span class="text-sm text-gray-600">待审核</span>
              </div>
              <span class="text-lg font-semibold text-gray-900">{{ task.pending_count || 0 }}</span>
            </div>
          </div>
        </UCard>
      </div>

      <!-- 学生提交情况 -->
      <UCard>
        <template #header>
          <div class="flex justify-between items-center">
            <h2 class="text-xl font-semibold text-gray-900">学生提交情况</h2>
            <UButton
              variant="outline"
              @click="navigateTo(`/teacher/submissions?task_id=${taskId}`)"
            >
              查看所有提交
            </UButton>
          </div>
        </template>

        <div v-if="submissionsLoading" class="text-center py-8">
          <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
          <span class="ml-2 text-gray-600">加载提交列表...</span>
        </div>

        <div v-else-if="!submissions?.length" class="text-center py-8">
          <UIcon name="i-heroicons-document-text" class="text-4xl text-gray-300 mb-4" />
          <p class="text-gray-500 mb-4">暂无学生提交</p>
        </div>

        <div v-else class="space-y-4">
          <div
            v-for="submission in submissions"
            :key="submission.submission_id"
            class="flex items-center justify-between p-4 border border-gray-200 rounded-lg hover:bg-gray-50"
          >
            <div class="flex items-center space-x-4">
              <div class="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <UIcon name="i-heroicons-user" class="text-blue-600" />
              </div>
              <div>
                <h3 class="font-medium text-gray-900">
                  {{ submission.student_name }}
                </h3>
                <p class="text-sm text-gray-500">{{ submission.student_email }}</p>
                <p v-if="submission.content" class="text-sm text-gray-600 mt-1 line-clamp-2">
                  {{ submission.content }}
                </p>
              </div>
            </div>

            <div class="flex items-center space-x-3">
              <div class="text-right">
                <UBadge
                  :color="getSubmissionStatusColor(submission.status)"
                  variant="soft"
                >
                  {{ getSubmissionStatusText(submission.status) }}
                </UBadge>
                <p class="text-xs text-gray-500 mt-1">
                  {{ formatDate(submission.submitted_at) }}
                </p>
              </div>

              <UDropdown :items="getSubmissionActions(submission)">
                <UButton
                  variant="ghost"
                  size="sm"
                  icon="i-heroicons-ellipsis-vertical"
                />
              </UDropdown>
            </div>
          </div>
        </div>
      </UCard>
    </div>

    <!-- 任务不存在 -->
    <div v-else class="text-center py-12">
      <UIcon name="i-heroicons-document-text" class="text-6xl text-gray-300 mb-4" />
      <h3 class="text-lg font-medium text-gray-900 mb-2">任务不存在</h3>
      <p class="text-gray-500 mb-4">请检查任务ID是否正确</p>
      <UButton @click="navigateTo('/teacher/tasks')" variant="outline">
        返回任务列表
      </UButton>
    </div>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 获取路由参数
const route = useRoute()
const taskId = parseInt(route.params.id as string)

// 添加调试信息
console.log('任务详情页面加载，任务ID:', taskId)

// 获取任务详情
const { data: taskResponse, pending, error, refresh } = await useAuthFetch(`/api/teacher/tasks/${taskId}`, {
  server: false,
  default: () => ({ success: false, data: null }),
  key: `task-${taskId}`
})

// 获取任务提交列表
const { data: submissionsResponse, pending: submissionsLoading } = await useAuthFetch(`/api/teacher/submissions?task_id=${taskId}`, {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取任务数据
const task = computed(() => (taskResponse.value as any)?.data || null)
const submissions = computed(() => (submissionsResponse.value as any)?.data || [])

// 编辑任务
const editTask = () => {
  navigateTo(`/teacher/tasks/${taskId}/edit`)
}

// 删除任务
const deleteTask = async () => {
  if (!confirm('确定要删除这个任务吗？此操作不可撤销。')) return
  
  try {
    const accessToken = useCookie('accessToken')
    await $fetch(`/api/teacher/tasks/${taskId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 返回任务列表
    navigateTo('/teacher/tasks')
  } catch (error) {
    console.error('删除任务失败:', error)
    // TODO: 显示错误消息
  }
}

// 获取提交状态颜色
const getSubmissionStatusColor = (status: string) => {
  switch (status) {
    case 'pending': return 'orange'
    case 'approved': return 'green'
    case 'rejected': return 'red'
    default: return 'gray'
  }
}

// 获取提交状态文本
const getSubmissionStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待审核'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    default: return '未知'
  }
}

// 获取提交操作菜单
const getSubmissionActions = (submission: any) => [
  [{
    label: '查看详情',
    icon: 'i-heroicons-eye',
    click: () => navigateTo(`/teacher/submissions/${submission.submission_id}`)
  }],
  [{
    label: '审核提交',
    icon: 'i-heroicons-clipboard-document-check',
    click: () => navigateTo(`/teacher/submissions/${submission.submission_id}`)
  }]
]

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// Set page title
useHead({
  title: computed(() => task.value ? `${task.value.title} - 任务详情` : '任务详情'),
  meta: [
    { name: 'description', content: '查看和管理任务详情、学生提交情况' }
  ]
})
</script>
