<template>
  <div class="container mx-auto px-4 py-8">
    <div class="flex justify-between items-center mb-8">
      <div>
        <h1 class="text-3xl font-bold text-gray-900">项目管理</h1>
        <p class="text-gray-600 mt-2">管理您的教学项目和学生分配</p>
      </div>
      <UButton @click="showCreateModal = true" color="primary">
        <UIcon name="i-heroicons-plus" class="mr-2" />
        创建项目
      </UButton>
    </div>

    <!-- 加载状态 -->
    <div v-if="pending" class="flex justify-center items-center py-12">
      <UIcon name="i-heroicons-arrow-path" class="animate-spin text-2xl text-blue-600" />
      <span class="ml-2 text-gray-600">加载中...</span>
    </div>

    <!-- 错误状态 -->
    <div v-else-if="error" class="text-center py-12">
      <UIcon name="i-heroicons-exclamation-triangle" class="text-4xl text-red-500 mb-4" />
      <p class="text-red-600 mb-4">加载项目列表时出现错误</p>
      <UButton @click="refresh()" variant="outline">重试</UButton>
    </div>

    <!-- 项目列表 -->
    <div v-else>
      <div v-if="!projects?.length" class="text-center py-12">
        <UIcon name="i-heroicons-folder-open" class="text-6xl text-gray-300 mb-4" />
        <h3 class="text-lg font-medium text-gray-900 mb-2">暂无项目</h3>
        <p class="text-gray-500 mb-4">开始创建您的第一个项目</p>
        <UButton @click="showCreateModal = true" color="primary">
          创建项目
        </UButton>
      </div>

      <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <UCard 
          v-for="project in projects" 
          :key="project.project_id"
          class="hover:shadow-lg transition-shadow cursor-pointer"
          @click="viewProject(project.project_id)"
        >
          <template #header>
            <div class="flex justify-between items-start">
              <h3 class="text-lg font-semibold text-gray-900 truncate">
                {{ project.project_name }}
              </h3>
              <UDropdown :items="getProjectActions(project)">
                <UButton 
                  variant="ghost" 
                  size="sm" 
                  icon="i-heroicons-ellipsis-vertical"
                  @click.stop
                />
              </UDropdown>
            </div>
          </template>

          <div class="space-y-3">
            <p v-if="project.description" class="text-gray-600 text-sm line-clamp-2">
              {{ project.description }}
            </p>
            <p v-else class="text-gray-400 text-sm italic">
              暂无描述
            </p>

            <div class="flex justify-between items-center text-sm">
              <div class="flex items-center text-gray-500">
                <UIcon name="i-heroicons-users" class="mr-1" />
                {{ project.student_count }} 名学生
              </div>
              <div class="flex items-center text-gray-500">
                <UIcon name="i-heroicons-document-text" class="mr-1" />
                {{ project.task_count }} 个任务
              </div>
            </div>

            <div class="text-xs text-gray-400">
              创建于 {{ formatDate(project.created_at) }}
            </div>
          </div>
        </UCard>
      </div>
    </div>

    <!-- 创建项目模态框 -->
    <UModal v-model="showCreateModal">
      <UCard>
        <template #header>
          <h3 class="text-lg font-semibold">创建新项目</h3>
        </template>

        <form @submit.prevent="createProject" class="space-y-4">
          <UFormGroup label="项目名称" required>
            <UInput 
              v-model="createForm.project_name" 
              placeholder="请输入项目名称"
              :disabled="creating"
            />
          </UFormGroup>

          <UFormGroup label="项目描述">
            <UTextarea 
              v-model="createForm.description" 
              placeholder="请输入项目描述（可选）"
              :disabled="creating"
              :rows="3"
            />
          </UFormGroup>

          <UFormGroup label="分配学生">
            <div v-if="studentsLoading" class="text-center py-4">
              <UIcon name="i-heroicons-arrow-path" class="animate-spin" />
              加载学生列表...
            </div>
            <div v-else-if="students?.length" class="space-y-2 max-h-40 overflow-y-auto">
              <label 
                v-for="student in students" 
                :key="student.user_id"
                class="flex items-center space-x-2 p-2 hover:bg-gray-50 rounded cursor-pointer"
              >
                <input 
                  type="checkbox" 
                  :value="student.user_id"
                  v-model="createForm.student_ids"
                  :disabled="creating"
                  class="rounded"
                />
                <span class="text-sm">
                  {{ student.full_name || student.username }} ({{ student.email }})
                </span>
              </label>
            </div>
            <p v-else class="text-gray-500 text-sm">暂无可分配的学生</p>
          </UFormGroup>

          <div class="flex justify-end space-x-3 pt-4">
            <UButton 
              variant="outline" 
              @click="showCreateModal = false"
              :disabled="creating"
            >
              取消
            </UButton>
            <UButton 
              type="submit" 
              color="primary"
              :loading="creating"
            >
              创建项目
            </UButton>
          </div>
        </form>
      </UCard>
    </UModal>
  </div>
</template>

<script setup lang="ts">
// Set page meta
definePageMeta({
  middleware: ['auth', 'role']
})

// 响应式数据
const showCreateModal = ref(false)
const creating = ref(false)

// 创建表单
const createForm = ref({
  project_name: '',
  description: '',
  student_ids: [] as number[]
})

// 获取项目列表
const { data: projectsResponse, pending, error, refresh } = await useAuthFetch('/api/teacher/projects', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 获取学生列表
const { data: studentsResponse, pending: studentsLoading } = await useAuthFetch('/api/teacher/students', {
  server: false,
  default: () => ({ success: true, data: [] })
})

// 提取实际数据
const projects = computed(() => (projectsResponse.value as any)?.data || [])
const students = computed(() => (studentsResponse.value as any)?.data || [])

// 创建项目
const createProject = async () => {
  creating.value = true
  try {
    const accessToken = useCookie('accessToken')
    const { data } = await $fetch('/api/teacher/projects', {
      method: 'POST',
      body: createForm.value,
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    if (data) {
      // 重置表单
      createForm.value = {
        project_name: '',
        description: '',
        student_ids: []
      }

      // 关闭模态框
      showCreateModal.value = false

      // 刷新项目列表
      await refresh()

      // 显示成功消息
      // TODO: 添加 toast 通知
    }
  } catch (error) {
    console.error('创建项目失败:', error)
    // TODO: 显示错误消息
  } finally {
    creating.value = false
  }
}

// 查看项目详情
const viewProject = (projectId: number) => {
  console.log('点击项目详情，项目ID:', projectId)
  console.log('即将跳转到:', `/teacher/projects/${projectId}`)
  navigateTo(`/teacher/projects/${projectId}`)
}

// 获取项目操作菜单
const getProjectActions = (project: any) => [
  [{
    label: '查看详情',
    icon: 'i-heroicons-eye',
    click: () => viewProject(project.project_id)
  }],
  [{
    label: '编辑项目',
    icon: 'i-heroicons-pencil',
    click: () => editProject(project.project_id)
  }],
  [{
    label: '删除项目',
    icon: 'i-heroicons-trash',
    click: () => deleteProject(project.project_id)
  }]
]

// 编辑项目
const editProject = (projectId: number) => {
  navigateTo(`/teacher/projects/${projectId}/edit`)
}

// 删除项目
const deleteProject = async (projectId: number) => {
  // TODO: 添加确认对话框
  try {
    const accessToken = useCookie('accessToken')
    await $fetch(`/api/teacher/projects/${projectId}`, {
      method: 'DELETE',
      headers: {
        'Authorization': `Bearer ${accessToken.value}`
      }
    })

    // 刷新项目列表
    await refresh()

    // TODO: 显示成功消息
  } catch (error) {
    console.error('删除项目失败:', error)
    // TODO: 显示错误消息
  }
}

// 格式化日期
const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('zh-CN')
}

// Set page title
useHead({
  title: '项目管理 - 学生任务管理系统'
})
</script>
